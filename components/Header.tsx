
import React from 'react';
import PlusIcon from './icons/PlusIcon';
import ThemeToggleButton from './ThemeToggleButton';
import QuestionMarkCircleIcon from './icons/QuestionMarkCircleIcon';
import DownloadIcon from './icons/DownloadIcon';
import UploadIcon from './icons/UploadIcon';
import IconButton from './IconButton';

interface HeaderProps {
  onAddPrompt: () => void;
  onToggleHelp: () => void;
  onExportData: () => void;
  onImportDataTrigger: () => void;
}

const Header: React.FC<HeaderProps> = ({ onAddPrompt, onToggleHelp, onExportData, onImportDataTrigger }) => {
  return (
    <header className="header">
      <div className="header-content">
        <div>
          <h1 className="app-title">
            <span role="img" aria-label="brain emoji" style={{ fontSize: '1.75rem' }}>🧠</span>
            <span className="hide-mobile">AI Prompt Manager</span>
            <span className="show-mobile">Prompts</span>
          </h1>
        </div>

        <div className="header-actions">
          <ThemeToggleButton />

          <div className="icon-group">
            <IconButton
              onClick={onImportDataTrigger}
              label="Import Data"
              className="icon-button"
              title="Import Data (replaces existing data)"
            >
              <UploadIcon className="icon" />
            </IconButton>

            <IconButton
              onClick={onExportData}
              label="Export Data"
              className="icon-button"
              title="Export All Data"
            >
              <DownloadIcon className="icon" />
            </IconButton>

            <IconButton
              onClick={onToggleHelp}
              label="Help"
              className="icon-button"
            >
              <QuestionMarkCircleIcon className="icon" />
            </IconButton>
          </div>

          <button
            onClick={onAddPrompt}
            className="button button-primary hide-mobile"
            title="Add New Prompt"
          >
            <PlusIcon className="icon" />
            <span className="hide-tablet">Add Prompt</span>
            <span className="show-tablet hide-mobile">Add</span>
          </button>

          <IconButton
            onClick={onAddPrompt}
            label="Add new prompt"
            className="button button-primary show-mobile"
            title="Add New Prompt"
          >
            <PlusIcon className="icon" />
          </IconButton>
        </div>
      </div>
    </header>
  );
};

export default Header;
