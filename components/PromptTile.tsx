import React, { useState } from 'react';
import { Prompt } from '../types';
import { formatDate } from '../utils/dateFormatter';
import IconButton from './IconButton';
import ClipboardIcon from './icons/ClipboardIcon';
import PencilIcon from './icons/PencilIcon';
import TrashIcon from './icons/TrashIcon';
import TagIcon from './icons/TagIcon';
import HistoryIcon from './icons/HistoryIcon';
import SparklesIcon from './icons/SparklesIcon'; // New icon for AI generation
import { extractVariables } from '../utils/templateUtils';

interface PromptTileProps {
  prompt: Prompt;
  onCopy: (prompt: Prompt) => void;
  onEdit: (prompt: Prompt) => void;
  onDelete: (originalPromptId: string) => void;
  onViewHistory: (originalPromptId: string) => void;
  onGenerateWithAI: (prompt: Prompt) => void; // New prop for AI generation
  hasMultipleVersions: boolean;
}

const PromptTile: React.FC<PromptTileProps> = ({ prompt, onCopy, onEdit, onDelete, onViewHistory, onGenerateWithAI, hasMultipleVersions }) => {
  const [copiedDirectly, setCopiedDirectly] = useState(false);

  const variables = extractVariables(prompt.promptText);
  const isTemplate = variables.length > 0;

  const handleAction = () => {
    if (isTemplate) {
      onCopy(prompt);
    } else {
      navigator.clipboard.writeText(prompt.promptText).then(() => {
        onCopy(prompt);
        setCopiedDirectly(true);
        setTimeout(() => setCopiedDirectly(false), 2000);
      }).catch(err => {
        console.error('Failed to copy text: ', err);
        alert('Failed to copy text.');
      });
    }
  };

  const buttonText = isTemplate
    ? "Use Template"
    : copiedDirectly
      ? "Copied!"
      : "Copy Prompt";

  return (
    <div className="card prompt-card">
      <div className="prompt-content">
        <div className="prompt-header">
          <h3 className="prompt-title" title={prompt.title}>
            {prompt.title}
          </h3>
          <div className="prompt-actions">
            {hasMultipleVersions && (
              <IconButton
                onClick={() => onViewHistory(prompt.originalPromptId)}
                label="View prompt history"
                className="icon-button"
              >
                <HistoryIcon className="icon" />
              </IconButton>
            )}
            <IconButton
              onClick={() => onGenerateWithAI(prompt)}
              label="Generate with AI"
              className="icon-button"
              title="Generate with AI"
            >
              <SparklesIcon className="icon" />
            </IconButton>
          </div>
        </div>

        <div className="prompt-details">
          <p className="prompt-version">Version {prompt.version}</p>
          <p className="prompt-description" title={prompt.description}>
            {prompt.description || "No description provided."}
          </p>

          {prompt.tags && prompt.tags.length > 0 && (
            <div className="prompt-tags">
              <TagIcon className="icon" />
              {prompt.tags.slice(0, 3).map(tag => (
                <span key={tag} className="tag">
                  {tag}
                </span>
              ))}
              {prompt.tags.length > 3 && (
                <span className="prompt-tags-more">+{prompt.tags.length - 3} more</span>
              )}
            </div>
          )}
        </div>
      </div>

      <div className="prompt-footer">
        <div className="prompt-stats">
          <div className="stat">
            <p className="stat-label">Used</p>
            <p className="stat-value">{prompt.timesUsed} times</p>
          </div>
          <div className="stat">
            <p className="stat-label">Last Used</p>
            <p className="stat-value">{formatDate(prompt.lastUsedAt)}</p>
          </div>
          <div className="stat">
            <p className="stat-label">Created</p>
            <p className="stat-value">{formatDate(prompt.createdAt)}</p>
          </div>
        </div>

        <div className="prompt-actions-footer">
          <button
            onClick={handleAction}
            className={`button ${(copiedDirectly && !isTemplate)
              ? 'button-success'
              : 'button-primary'}`}
          >
            <ClipboardIcon className="icon" />
            {buttonText}
          </button>
          <div className="prompt-edit-actions">
            <IconButton
              onClick={() => onEdit(prompt)}
              label="Edit prompt (creates new version)"
              className="icon-button"
            >
              <PencilIcon className="icon" />
            </IconButton>
            <IconButton
              onClick={() => onDelete(prompt.originalPromptId)}
              label="Delete prompt (all versions)"
              className="icon-button icon-button-danger"
            >
              <TrashIcon className="icon" />
            </IconButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromptTile;