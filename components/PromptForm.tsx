import React, { useState, useEffect, useMemo } from 'react';
import { Prompt, Folder } from '../types';
import TagInput from './TagInput';
import { DEFAULT_FOLDER_NAME } from '../constants';
import { buildFolderHierarchy, getFlattenedFolders } from '../utils/folderUtils';
import { MAX_TITLE_LENGTH, MAX_DESCRIPTION_LENGTH, MAX_PROMPT_TEXT_LENGTH, sanitizeInput, validatePromptData } from '../utils/validation';


interface PromptFormProps {
  promptToEdit?: Prompt | null;
  folders: Folder[];
  onSave: (promptData: Omit<Prompt, 'id' | 'originalPromptId' | 'version' | 'createdAt' | 'lastUsedAt' | 'timesUsed'>, existingPrompt?: Prompt | null) => Promise<void>;
  onClose: () => void;
  defaultFolderId: string | null;
}

const PromptForm: React.FC<PromptFormProps> = ({ promptToEdit, folders, onSave, onClose, defaultFolderId }) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [promptText, setPromptText] = useState('');
  const [tags, setTags] = useState<string[]>([]);
  const [folderId, setFolderId] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string>('');

  // Validation states
  const [titleError, setTitleError] = useState<string>('');
  const [descriptionError, setDescriptionError] = useState<string>('');
  const [promptTextError, setPromptTextError] = useState<string>('');

  const hierarchicalFoldersForSelect = useMemo(() => {
    // Filter out the default "Uncategorized" folder before building hierarchy for user-selectable folders
    const userFolders = folders.filter(f => f.id !== defaultFolderId);
    const hierarchy = buildFolderHierarchy(userFolders);
    return getFlattenedFolders(hierarchy);
  }, [folders, defaultFolderId]);

  // The default folder option should always be available and represent the "Uncategorized" folder
  const defaultFolderOption = useMemo(() => folders.find(f => f.id === defaultFolderId), [folders, defaultFolderId]);


  useEffect(() => {
    if (promptToEdit) {
      setTitle(promptToEdit.title);
      setDescription(promptToEdit.description);
      setPromptText(promptToEdit.promptText);
      setTags(promptToEdit.tags);
      setFolderId(promptToEdit.folderId); // This could be defaultFolderId or a user folder
    } else {
      // For new prompts, default to the "Uncategorized" folder
      setFolderId(defaultFolderId);
    }
  }, [promptToEdit, defaultFolderId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError('');
    setIsSubmitting(true);

    console.log(`[PromptForm] Attempting to ${promptToEdit ? 'update' : 'create'} prompt: "${title}"`);

    try {
      // Check for validation errors
      if (titleError || descriptionError || promptTextError) {
        const errors = [titleError, descriptionError, promptTextError].filter(Boolean);
        console.warn('[PromptForm] Form validation failed:', errors);
        throw new Error('Please fix all validation errors before submitting.');
      }

      // Validate all prompt data
      const errors = validatePromptData(
        title,
        description,
        promptText,
        [], // TODO: Pass existing prompts for duplicate title check
        !promptToEdit
      );

      if (errors.length > 0) {
        console.warn('[PromptForm] Prompt data validation failed:', errors);
        throw new Error(errors.join('\n'));
      }

      const promptData = {
        title: title.trim(),
        description: description.trim(),
        promptText: promptText.trim(),
        tags,
        folderId: folderId,
      };

      console.log('[PromptForm] Validation passed, saving prompt...', {
        action: promptToEdit ? 'update' : 'create',
        title: promptData.title,
        hasDescription: !!promptData.description,
        tagCount: tags.length,
        folderId: folderId || 'default'
      });

      await onSave(promptData, promptToEdit);
      console.log('[PromptForm] Successfully saved prompt:', promptData.title);
      onClose();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred while saving the prompt.';
      console.error('[PromptForm] Error saving prompt:', {
        error: errorMessage,
        title: title.trim(),
        isUpdate: !!promptToEdit
      });
      setFormError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label htmlFor="title" className="form-label">
          Title
        </label>
        <input
          type="text"
          id="title"
          value={title}
          onChange={(e) => {
            const sanitized = sanitizeInput(e.target.value);
            setTitle(sanitized);
            if (sanitized.length === 0) {
              setTitleError('Title is required');
            } else if (sanitized.length > MAX_TITLE_LENGTH) {
              setTitleError(`Title must be ${MAX_TITLE_LENGTH} characters or less`);
            } else {
              setTitleError('');
            }
          }}
          maxLength={MAX_TITLE_LENGTH}
          required
          className={`form-input ${titleError ? 'error' : ''}`}
        />
        {titleError && <p className="form-error">{titleError}</p>}
      </div>

      <div>
        <label htmlFor="description" className="form-label">
          Short Description (Optional)
        </label>
        <textarea
          id="description"
          value={description}
          onChange={(e) => {
            const sanitized = sanitizeInput(e.target.value);
            setDescription(sanitized);
            if (sanitized.length > MAX_DESCRIPTION_LENGTH) {
              setDescriptionError(`Description must be ${MAX_DESCRIPTION_LENGTH} characters or less`);
            } else {
              setDescriptionError('');
            }
          }}
          maxLength={MAX_DESCRIPTION_LENGTH}
          rows={2}
          className={`form-textarea ${descriptionError ? 'error' : ''}`}
        />
        {descriptionError && <p className="form-error">{descriptionError}</p>}
      </div>

      <div>
        <label htmlFor="promptText" className="form-label">
          Prompt Text
          <span className="form-label-secondary">(Use &#123;&#123;variable_name&#125;&#125; for templates)</span>
        </label>
        <textarea
          id="promptText"
          value={promptText}
          onChange={(e) => {
            const sanitized = sanitizeInput(e.target.value);
            setPromptText(sanitized);
            if (sanitized.length === 0) {
              setPromptTextError('Prompt text is required');
            } else if (sanitized.length > MAX_PROMPT_TEXT_LENGTH) {
              setPromptTextError(`Prompt text must be ${MAX_PROMPT_TEXT_LENGTH} characters or less`);
            } else {
              setPromptTextError('');
            }
          }}
          maxLength={MAX_PROMPT_TEXT_LENGTH}
          rows={6}
          required
          className={`form-textarea ${promptTextError ? 'error' : ''}`}
          placeholder="Enter your AI prompt. Example: Write a blog post about {{topic}}."
        />
        {promptTextError && <p className="form-error">{promptTextError}</p>}
      </div>

      <TagInput tags={tags} onTagsChange={setTags} />

      <div>
        <label htmlFor="folder" className="form-label">
          Folder
        </label>
        <select
          id="folder"
          value={folderId || ''} // Ensure value is correctly handled if folderId is null (should be defaultFolderId's value)
          onChange={(e) => setFolderId(e.target.value || null )} // If '' selected, treat as default/uncategorized
          className="form-select"
        >
          {defaultFolderOption && <option value={defaultFolderOption.id}>{DEFAULT_FOLDER_NAME}</option>}
          {/* If somehow defaultFolderOption is not available but defaultFolderId is, provide a fallback.
              This case should ideally not happen if defaultFolderId is always set from a valid folder. */}
          {!defaultFolderOption && defaultFolderId && <option value={defaultFolderId}>{DEFAULT_FOLDER_NAME} (Default)</option>}

          {hierarchicalFoldersForSelect.map(folder => (
            // The style for indentation might need adjustment based on final CSS processing of Tailwind classes for option
            <option key={folder.id} value={folder.id} style={{ paddingLeft: `${(folder.level || 0) * 10 + 5}px` }}>
              {folder.name}
            </option>
          ))}
        </select>
      </div>

      {formError && (
        <div className="form-error-container">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="form-error-icon" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <p className="form-error-text">{formError}</p>
            </div>
          </div>
        </div>
      )}

      <div className="form-buttons">
        <button
          type="button"
          onClick={onClose}
          className="button-cancel"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className={`button-primary ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          {isSubmitting ? 'Saving...' : (promptToEdit ? 'Save as New Version' : 'Create Prompt')}
        </button>
      </div>
    </form>
  );
};

export default PromptForm;
