import React, { useState, useEffect, useMemo } from 'react';
import { Prompt, Folder } from '../types';
import TagInput from './TagInput';
import { DEFAULT_FOLDER_NAME } from '../constants';
import { buildFolderHierarchy, getFlattenedFolders } from '../utils/folderUtils';
import { MAX_TITLE_LENGTH, MAX_DESCRIPTION_LENGTH, MAX_PROMPT_TEXT_LENGTH, sanitizeInput, validatePromptData } from '../utils/validation';


interface PromptFormProps {
  promptToEdit?: Prompt | null;
  folders: Folder[];
  onSave: (promptData: Omit<Prompt, 'id' | 'originalPromptId' | 'version' | 'createdAt' | 'lastUsedAt' | 'timesUsed'>, existingPrompt?: Prompt | null) => Promise<void>;
  onClose: () => void;
  defaultFolderId: string | null;
}

const PromptForm: React.FC<PromptFormProps> = ({ promptToEdit, folders, onSave, onClose, defaultFolderId }) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [promptText, setPromptText] = useState('');
  const [tags, setTags] = useState<string[]>([]);
  const [folderId, setFolderId] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string>('');

  // Validation states
  const [titleError, setTitleError] = useState<string>('');
  const [descriptionError, setDescriptionError] = useState<string>('');
  const [promptTextError, setPromptTextError] = useState<string>('');

  const hierarchicalFoldersForSelect = useMemo(() => {
    // Filter out the default "Uncategorized" folder before building hierarchy for user-selectable folders
    const userFolders = folders.filter(f => f.id !== defaultFolderId);
    const hierarchy = buildFolderHierarchy(userFolders);
    return getFlattenedFolders(hierarchy);
  }, [folders, defaultFolderId]);

  // The default folder option should always be available and represent the "Uncategorized" folder
  const defaultFolderOption = useMemo(() => folders.find(f => f.id === defaultFolderId), [folders, defaultFolderId]);


  useEffect(() => {
    if (promptToEdit) {
      setTitle(promptToEdit.title);
      setDescription(promptToEdit.description);
      setPromptText(promptToEdit.promptText);
      setTags(promptToEdit.tags);
      setFolderId(promptToEdit.folderId); // This could be defaultFolderId or a user folder
    } else {
      // For new prompts, default to the "Uncategorized" folder
      setFolderId(defaultFolderId);
    }
  }, [promptToEdit, defaultFolderId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError('');
    setIsSubmitting(true);

    console.log(`[PromptForm] Attempting to ${promptToEdit ? 'update' : 'create'} prompt: "${title}"`);

    try {
      // Check for validation errors
      if (titleError || descriptionError || promptTextError) {
        const errors = [titleError, descriptionError, promptTextError].filter(Boolean);
        console.warn('[PromptForm] Form validation failed:', errors);
        throw new Error('Please fix all validation errors before submitting.');
      }

      // Validate all prompt data
      const errors = validatePromptData(
        title,
        description,
        promptText,
        [], // TODO: Pass existing prompts for duplicate title check
        !promptToEdit
      );

      if (errors.length > 0) {
        console.warn('[PromptForm] Prompt data validation failed:', errors);
        throw new Error(errors.join('\n'));
      }

      const promptData = {
        title: title.trim(),
        description: description.trim(),
        promptText: promptText.trim(),
        tags,
        folderId: folderId,
      };

      console.log('[PromptForm] Validation passed, saving prompt...', {
        action: promptToEdit ? 'update' : 'create',
        title: promptData.title,
        hasDescription: !!promptData.description,
        tagCount: tags.length,
        folderId: folderId || 'default'
      });

      await onSave(promptData, promptToEdit);
      console.log('[PromptForm] Successfully saved prompt:', promptData.title);
      onClose();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred while saving the prompt.';
      console.error('[PromptForm] Error saving prompt:', {
        error: errorMessage,
        title: title.trim(),
        isUpdate: !!promptToEdit
      });
      setFormError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label htmlFor="title" className="block text-sm font-medium text-neutral-700 dark:text-neutral-300">
          Title
        </label>
        <input
          type="text"
          id="title"
          value={title}
          onChange={(e) => {
            const sanitized = sanitizeInput(e.target.value);
            setTitle(sanitized);
            if (sanitized.length === 0) {
              setTitleError('Title is required');
            } else if (sanitized.length > MAX_TITLE_LENGTH) {
              setTitleError(`Title must be ${MAX_TITLE_LENGTH} characters or less`);
            } else {
              setTitleError('');
            }
          }}
          maxLength={MAX_TITLE_LENGTH}
          required
          className={`mt-1 block w-full px-3 py-2 border ${titleError ? 'border-red-500' : 'border-neutral-300 dark:border-neutral-600'} rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100`}
        />
        {titleError && <p className="mt-1 text-sm text-red-500">{titleError}</p>}
      </div>

      <div>
        <label htmlFor="description" className="block text-sm font-medium text-neutral-700 dark:text-neutral-300">
          Short Description (Optional)
        </label>
        <textarea
          id="description"
          value={description}
          onChange={(e) => {
            const sanitized = sanitizeInput(e.target.value);
            setDescription(sanitized);
            if (sanitized.length > MAX_DESCRIPTION_LENGTH) {
              setDescriptionError(`Description must be ${MAX_DESCRIPTION_LENGTH} characters or less`);
            } else {
              setDescriptionError('');
            }
          }}
          maxLength={MAX_DESCRIPTION_LENGTH}
          rows={2}
          className={`mt-1 block w-full px-3 py-2 border ${descriptionError ? 'border-red-500' : 'border-neutral-300 dark:border-neutral-600'} rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100`}
        />
        {descriptionError && <p className="mt-1 text-sm text-red-500">{descriptionError}</p>}
      </div>

      <div>
        <label htmlFor="promptText" className="block text-sm font-medium text-neutral-700 dark:text-neutral-300">
          Prompt Text
          <span className="text-xs text-neutral-500 dark:text-neutral-400 ml-1">(Use &#123;&#123;variable_name&#125;&#125; for templates)</span>
        </label>
        <textarea
          id="promptText"
          value={promptText}
          onChange={(e) => {
            const sanitized = sanitizeInput(e.target.value);
            setPromptText(sanitized);
            if (sanitized.length === 0) {
              setPromptTextError('Prompt text is required');
            } else if (sanitized.length > MAX_PROMPT_TEXT_LENGTH) {
              setPromptTextError(`Prompt text must be ${MAX_PROMPT_TEXT_LENGTH} characters or less`);
            } else {
              setPromptTextError('');
            }
          }}
          maxLength={MAX_PROMPT_TEXT_LENGTH}
          rows={6}
          required
          className={`mt-1 block w-full px-3 py-2 border ${promptTextError ? 'border-red-500' : 'border-neutral-300 dark:border-neutral-600'} rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100`}
          placeholder="Enter your AI prompt. Example: Write a blog post about {{topic}}."
        />
        {promptTextError && <p className="mt-1 text-sm text-red-500">{promptTextError}</p>}
      </div>

      <TagInput tags={tags} onTagsChange={setTags} />

      <div>
        <label htmlFor="folder" className="block text-sm font-medium text-neutral-700 dark:text-neutral-300">
          Folder
        </label>
        <select
          id="folder"
          value={folderId || ''} // Ensure value is correctly handled if folderId is null (should be defaultFolderId's value)
          onChange={(e) => setFolderId(e.target.value || null )} // If '' selected, treat as default/uncategorized
          className="mt-1 block w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 bg-white dark:bg-neutral-700 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-neutral-900 dark:text-neutral-100"
        >
          {defaultFolderOption && <option value={defaultFolderOption.id}>{DEFAULT_FOLDER_NAME}</option>}
          {/* If somehow defaultFolderOption is not available but defaultFolderId is, provide a fallback.
              This case should ideally not happen if defaultFolderId is always set from a valid folder. */}
          {!defaultFolderOption && defaultFolderId && <option value={defaultFolderId}>{DEFAULT_FOLDER_NAME} (Default)</option>}

          {hierarchicalFoldersForSelect.map(folder => (
            // The style for indentation might need adjustment based on final CSS processing of Tailwind classes for option
            <option key={folder.id} value={folder.id} style={{ paddingLeft: `${(folder.level || 0) * 10 + 5}px` }}>
              {folder.name}
            </option>
          ))}
        </select>
      </div>

      {formError && (
        <div className="rounded-md bg-red-50 dark:bg-red-900/30 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700 dark:text-red-200">{formError}</p>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-end space-x-3 pt-2">
        <button
          type="button"
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium text-neutral-700 dark:text-neutral-200 bg-neutral-100 dark:bg-neutral-600 border border-neutral-300 dark:border-neutral-500 rounded-md shadow-sm hover:bg-neutral-200 dark:hover:bg-neutral-500 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-neutral-800 focus:ring-primary-light"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className={`px-4 py-2 text-sm font-medium text-white bg-primary hover:bg-primary-dark border border-transparent rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-neutral-800 focus:ring-primary ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          {isSubmitting ? 'Saving...' : (promptToEdit ? 'Save as New Version' : 'Create Prompt')}
        </button>
      </div>
    </form>
  );
};

export default PromptForm;
