import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import HelpModal from '../HelpModal';

describe('HelpModal', () => {
  const mockOnClose = jest.fn();

  beforeEach(() => {
    mockOnClose.mockClear();
  });

  it('should not render when isOpen is false', () => {
    render(<HelpModal isOpen={false} onClose={mockOnClose} />);
    expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
  });

  it('should render when isOpen is true', () => {
    render(<HelpModal isOpen={true} onClose={mockOnClose} />);
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getByText('Help & Information')).toBeInTheDocument();
  });

  it('should show User Guide content by default', () => {
    render(<HelpModal isOpen={true} onClose={mockOnClose} />);
    expect(screen.getByText('Welcome to your AI Prompt Manager!')).toBeInTheDocument();
    expect(screen.getByText('Core Features')).toBeInTheDocument();
  });

  it('should switch between User Guide and Advanced Info tabs', async () => {
    render(<HelpModal isOpen={true} onClose={mockOnClose} />);

    // Initial state - User Guide
    expect(screen.getByText('Welcome to your AI Prompt Manager!')).toBeInTheDocument();

    // Switch to Advanced Info
    const advancedTab = screen.getByRole('button', { name: 'Advanced Info / Data' });
    await userEvent.click(advancedTab);

    // Check Advanced Info content is shown
    expect(screen.getByText('Advanced Information & Data Structure')).toBeInTheDocument();
    expect(screen.getByText('Storage Mechanism')).toBeInTheDocument();

    // Switch back to User Guide
    const userGuideTab = screen.getByRole('button', { name: 'User Guide' });
    await userEvent.click(userGuideTab);

    // Check User Guide content is shown again
    expect(screen.getByText('Welcome to your AI Prompt Manager!')).toBeInTheDocument();
  });

  it('should call onClose when close button is clicked', async () => {
    render(<HelpModal isOpen={true} onClose={mockOnClose} />);

    const closeButton = screen.getByRole('button', { name: 'Close modal' });
    await userEvent.click(closeButton);

    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('should render all core feature sections', () => {
    render(<HelpModal isOpen={true} onClose={mockOnClose} />);

    const expectedSections = [
      'Core Features',
      'Prompt Versioning',
      'Prompt Templating & Variables',
      'Sorting & Filtering',
      'Light/Dark Mode',
      'Import & Export Data'
    ];

    expectedSections.forEach(section => {
      expect(screen.getByText(section)).toBeInTheDocument();
    });
  });

  it('should render all advanced info sections when switched', async () => {
    render(<HelpModal isOpen={true} onClose={mockOnClose} />);

    const advancedTab = screen.getByRole('button', { name: 'Advanced Info / Data' });
    await userEvent.click(advancedTab);

    const expectedSections = [
      'Storage Mechanism',
      'Data Structures (as used in Export/Import)',
      'Data Portability (Import/Export)',
      '🤖 AI Text Generation with Ollama (Advanced)'
    ];

    expectedSections.forEach(section => {
      expect(screen.getByText(section)).toBeInTheDocument();
    });
  });

  it('should maintain proper tab styling when switching', async () => {
    render(<HelpModal isOpen={true} onClose={mockOnClose} />);

    const userGuideTab = screen.getByRole('button', { name: 'User Guide' });
    const advancedTab = screen.getByRole('button', { name: 'Advanced Info / Data' });

    // Initially User Guide should be active
    expect(userGuideTab).toHaveClass('border-primary');
    expect(advancedTab).toHaveClass('border-transparent');

    // Switch to Advanced
    await userEvent.click(advancedTab);
    expect(advancedTab).toHaveClass('border-primary');
    expect(userGuideTab).toHaveClass('border-transparent');

    // Switch back to User Guide
    await userEvent.click(userGuideTab);
    expect(userGuideTab).toHaveClass('border-primary');
    expect(advancedTab).toHaveClass('border-transparent');
  });

  // Accessibility tests
  it('should have proper ARIA attributes', () => {
    render(<HelpModal isOpen={true} onClose={mockOnClose} />);

    const dialog = screen.getByRole('dialog');
    expect(dialog).toBeInTheDocument();
    expect(dialog).toHaveAttribute('aria-modal', 'true');
    expect(dialog).toHaveAttribute('aria-labelledby', 'modal-title');

    const closeButton = screen.getByRole('button', { name: 'Close modal' });
    expect(closeButton).toHaveAttribute('aria-label', 'Close modal');
  });
});