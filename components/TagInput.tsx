import React, { useState } from 'react';

interface TagInputProps {
  tags: string[];
  onTagsChange: (tags: string[]) => void;
  label?: string;
}

const TagInput: React.FC<TagInputProps> = ({ tags, onTagsChange, label = "Tags" }) => {
  const [inputValue, setInputValue] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if ((e.key === 'Enter' || e.key === ',') && inputValue.trim() !== '') {
      e.preventDefault();
      const newTag = inputValue.trim();
      if (!tags.includes(newTag)) {
        onTagsChange([...tags, newTag]);
      }
      setInputValue('');
    } else if (e.key === 'Backspace' && inputValue === '' && tags.length > 0) {
      onTagsChange(tags.slice(0, -1));
    }
  };

  const removeTag = (tagToRemove: string) => {
    onTagsChange(tags.filter(tag => tag !== tagToRemove));
  };

  return (
    <div>
      <label htmlFor="tag-input" className="form-label">
        {label} (comma or Enter to add)
      </label>
      <div className="tag-input-container">
        {tags.map(tag => (
          <span key={tag} className="tag-item">
            {tag}
            <button
              type="button"
              onClick={() => removeTag(tag)}
              className="tag-remove-button"
              aria-label={`Remove ${tag}`}
            >
              &times;
            </button>
          </span>
        ))}
        <input
          id="tag-input"
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleInputKeyDown}
          className="tag-input-field"
          placeholder={tags.length === 0 ? "Add tags..." : ""}
        />
      </div>
    </div>
  );
};

export default TagInput;