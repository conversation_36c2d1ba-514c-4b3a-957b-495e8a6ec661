export const MAX_TITLE_LENGTH = 100;
export const MAX_DESCRIPTION_LENGTH = 500;
export const MAX_PROMPT_TEXT_LENGTH = 5000;

export function sanitizeInput(input: string): string {
  // Remove any HTML tags
  let sanitized = input.replace(/<[^>]*>/g, '');

  // Remove control characters and zero-width spaces
  sanitized = sanitized.replace(/[\u0000-\u001F\u007F-\u009F\u200B-\u200D\uFEFF]/g, '');

  // Normalize whitespace
  sanitized = sanitized.replace(/\s+/g, ' ').trim();

  return sanitized;
}

export function validatePromptData(
  title: string,
  description: string,
  promptText: string,
  existingPrompts: { title: string }[],
  isNewPrompt: boolean
): string[] {
  const errors: string[] = [];

  // Sanitize inputs
  const sanitizedTitle = sanitizeInput(title);
  const sanitizedDesc = sanitizeInput(description);
  const sanitizedPrompt = sanitizeInput(promptText);

  // Length validations
  if (sanitizedTitle.length === 0) {
    errors.push('Title is required');
  } else if (sanitizedTitle.length > MAX_TITLE_LENGTH) {
    errors.push(`Title must be ${MAX_TITLE_LENGTH} characters or less`);
  }

  if (sanitizedDesc.length > MAX_DESCRIPTION_LENGTH) {
    errors.push(`Description must be ${MAX_DESCRIPTION_LENGTH} characters or less`);
  }

  if (sanitizedPrompt.length === 0) {
    errors.push('Prompt text is required');
  } else if (sanitizedPrompt.length > MAX_PROMPT_TEXT_LENGTH) {
    errors.push(`Prompt text must be ${MAX_PROMPT_TEXT_LENGTH} characters or less`);
  }

  // Duplicate title check for new prompts
  if (isNewPrompt && existingPrompts.some(p => p.title.toLowerCase() === sanitizedTitle.toLowerCase())) {
    errors.push('A prompt with this title already exists');
  }

  return errors;
}