

import { openDB, IDBPDatabase, IDBPTransaction } from 'idb';
import { Prompt, Folder, AIPMPRDB, ExportData } from '../types';
import { generateUUID } from './uuid';
import { DEFAULT_FOLDER_NAME } from '../constants';

const DB_NAME = 'AIPromptManagerDB';
const DB_VERSION = 1;
const PROMPTS_STORE_NAME = 'prompts';
const FOLDERS_STORE_NAME = 'folders';

let dbPromise: Promise<IDBPDatabase<AIPMPRDB>> | null = null;

// Reset the database promise to force a new connection
const resetDBPromise = () => {
  dbPromise = null;
};

const deleteDatabase = async (): Promise<void> => {
  console.log('Attempting to delete existing database...');
  const deleteRequest = window.indexedDB.deleteDatabase(DB_NAME);
  return new Promise<void>((resolve, reject) => {
    deleteRequest.onerror = () => {
      console.error('Error deleting database');
      reject(new Error('Failed to delete database'));
    };
    deleteRequest.onsuccess = () => {
      console.log('Database deleted successfully');
      resolve();
    };
  });
};

const getDB = (): Promise<IDBPDatabase<AIPMPRDB>> => {
  console.log('Checking IndexedDB availability...');
  if (!window.indexedDB) {
    console.error('IndexedDB is not supported in this browser');
    return Promise.reject(new Error('IndexedDB is not supported'));
  }

  if (!dbPromise) {
    console.log('Initializing IndexedDB connection...');
    dbPromise = openDB<AIPMPRDB>(DB_NAME, DB_VERSION, {
      upgrade(db: IDBPDatabase<AIPMPRDB>, oldVersion: number, newVersion: number | null, tx: IDBPTransaction<AIPMPRDB, (typeof PROMPTS_STORE_NAME | typeof FOLDERS_STORE_NAME)[], "versionchange">, event: IDBVersionChangeEvent) {
        console.log(`Upgrading DB from version ${oldVersion} to ${newVersion}`);
        if (!db.objectStoreNames.contains(PROMPTS_STORE_NAME)) {
          const promptsStore = db.createObjectStore(PROMPTS_STORE_NAME, { keyPath: 'id' });
          promptsStore.createIndex('originalPromptId', 'originalPromptId');
          promptsStore.createIndex('folderId', 'folderId');
          promptsStore.createIndex('version', 'version');
          promptsStore.createIndex('createdAt', 'createdAt');
          promptsStore.createIndex('lastUsedAt', 'lastUsedAt');
          promptsStore.createIndex('title', 'title');
        }
        if (!db.objectStoreNames.contains(FOLDERS_STORE_NAME)) {
          const foldersStore = db.createObjectStore(FOLDERS_STORE_NAME, { keyPath: 'id' });
          foldersStore.createIndex('parentId', 'parentId');
          foldersStore.createIndex('name', 'name');
        }
      },
      blocked(currentVersion: number, blockedVersion: number | null, event: IDBVersionChangeEvent) {
        console.error(`IndexedDB open blocked. Current version: ${currentVersion}, Attempted version: ${blockedVersion}. Please close other tabs running this application and refresh.`);
        alert("The application database is blocked by an older version in another tab. Please close other tabs and refresh the page.");
      },
      blocking(this: IDBPDatabase<AIPMPRDB>, currentVersion: number, blockedVersion: number | null, event: IDBVersionChangeEvent) {
        console.warn(`IndexedDB connection (version ${currentVersion}) is blocking an attempt to open version ${blockedVersion}. Closing this blocking connection.`);
        this.close();
        alert("The application database needs to upgrade. This tab was blocking it and has closed its database connection. Please refresh the page for the changes to take effect.");
      },
      terminated() {
        console.warn('IndexedDB connection was terminated by the browser.');
        dbPromise = null;
      }
    }).catch(async error => {
      console.error("Failed to open IndexedDB:", error);
      console.error("Browser:", navigator.userAgent);
      console.error("IndexedDB State:", {
        factory: window.indexedDB,
        databases: 'databases' in window.indexedDB
      });

      // If we get a version error, try to delete and recreate the database
      if (error.name === 'VersionError') {
        console.log('Version mismatch detected, attempting recovery...');
        try {
          await deleteDatabase();
          console.log('Attempting to recreate database...');
          resetDBPromise();
          // Create a new database connection
          const db = await openDB<AIPMPRDB>(DB_NAME, DB_VERSION, {
            upgrade(db: IDBPDatabase<AIPMPRDB>, oldVersion: number, newVersion: number | null) {
              console.log(`Upgrading DB from version ${oldVersion} to ${newVersion}`);
              if (!db.objectStoreNames.contains(PROMPTS_STORE_NAME)) {
                const promptsStore = db.createObjectStore(PROMPTS_STORE_NAME, { keyPath: 'id' });
                promptsStore.createIndex('originalPromptId', 'originalPromptId');
                promptsStore.createIndex('folderId', 'folderId');
                promptsStore.createIndex('version', 'version');
                promptsStore.createIndex('createdAt', 'createdAt');
                promptsStore.createIndex('lastUsedAt', 'lastUsedAt');
                promptsStore.createIndex('title', 'title');
              }
              if (!db.objectStoreNames.contains(FOLDERS_STORE_NAME)) {
                const foldersStore = db.createObjectStore(FOLDERS_STORE_NAME, { keyPath: 'id' });
                foldersStore.createIndex('parentId', 'parentId');
                foldersStore.createIndex('name', 'name');
              }
            }
          });
          dbPromise = Promise.resolve(db);
          return db;
        } catch (retryError) {
          console.error("Failed to recover from version mismatch:", retryError);
          resetDBPromise();
          throw new Error('Unable to initialize database after version mismatch recovery attempt. Please clear your browser data and try again.');
        }
      }

      dbPromise = null;
      throw error;
    });
  }
  return dbPromise;
};


export const initDefaultFolderDB = async (): Promise<Folder> => {
  const db = await getDB();
  const tx = db.transaction(FOLDERS_STORE_NAME, 'readwrite');
  const store = tx.objectStore(FOLDERS_STORE_NAME);
  let defaultFolder = (await store.getAll()).find(f => f.name === DEFAULT_FOLDER_NAME && f.parentId === null);

  if (!defaultFolder) {
    defaultFolder = {
      id: generateUUID(),
      name: DEFAULT_FOLDER_NAME,
      parentId: null,
      isDeletable: false,
      isRenamable: false,
    };
    await store.add(defaultFolder);
  }
  await tx.done;
  return defaultFolder;
};


// Prompt Functions
export const getAllPromptsDB = async (): Promise<Prompt[]> => {
  console.log('Attempting to get all prompts...');
  try {
    const db = await getDB();
    console.log('DB connection successful, retrieving prompts...');
    const prompts = await db.getAll(PROMPTS_STORE_NAME);
    console.log(`Successfully retrieved ${prompts.length} prompts`);
    return prompts;
  } catch (error) {
    console.error('Failed to get prompts:', error);
    throw error;
  }
};

export const getPromptByIdDB = async (id: string): Promise<Prompt | undefined> => {
  const db = await getDB();
  return db.get(PROMPTS_STORE_NAME, id);
};

export const getPromptsByOriginalIdDB = async (originalPromptId: string): Promise<Prompt[]> => {
  const db = await getDB();
  return db.getAllFromIndex(PROMPTS_STORE_NAME, 'originalPromptId', originalPromptId);
};

export const addPromptDB = async (prompt: Prompt): Promise<string> => {
  const db = await getDB();
  return db.add(PROMPTS_STORE_NAME, prompt);
};

export const updatePromptDB = async (prompt: Prompt): Promise<string> => {
  const db = await getDB();
  return db.put(PROMPTS_STORE_NAME, prompt);
};

export const deletePromptsByOriginalIdDB = async (originalPromptId: string): Promise<void> => {
  const db = await getDB();
  const tx = db.transaction(PROMPTS_STORE_NAME, 'readwrite');
  const store = tx.objectStore(PROMPTS_STORE_NAME);
  const index = store.index('originalPromptId');
  let cursor = await index.openCursor(originalPromptId);
  while (cursor) {
    await store.delete(cursor.primaryKey);
    cursor = await cursor.continue();
  }
  await tx.done;
};

export const clearAllPromptsDB = async (): Promise<void> => {
  const db = await getDB();
  await db.clear(PROMPTS_STORE_NAME);
};

// Folder Functions
export const getAllFoldersDB = async (): Promise<Folder[]> => {
  const db = await getDB();
  return db.getAll(FOLDERS_STORE_NAME);
};

export const addFolderDB = async (folder: Folder): Promise<string> => {
  const db = await getDB();
  return db.add(FOLDERS_STORE_NAME, folder);
};

export const updateFolderDB = async (folder: Folder): Promise<string> => {
  const db = await getDB();
  return db.put(FOLDERS_STORE_NAME, folder);
};

export const deleteFolderDB = async (folderId: string): Promise<void> => {
  await deleteFoldersDB([folderId]);
};

export const deleteFoldersDB = async (folderIds: string[]): Promise<void> => {
  const db = await getDB();
  const tx = db.transaction(FOLDERS_STORE_NAME, 'readwrite');
  const store = tx.objectStore(FOLDERS_STORE_NAME);
  await Promise.all(folderIds.map(id => store.delete(id)));
  await tx.done;
};

export const getFolderByIdDB = async (id: string): Promise<Folder | undefined> => {
    const db = await getDB();
    return db.get(FOLDERS_STORE_NAME, id);
};

export const importDataDB = async (data: ExportData): Promise<void> => {
  const db = await getDB();
  const tx = db.transaction([PROMPTS_STORE_NAME, FOLDERS_STORE_NAME], 'readwrite');

  const promptsStore = tx.objectStore(PROMPTS_STORE_NAME);
  const foldersStore = tx.objectStore(FOLDERS_STORE_NAME);

  await promptsStore.clear();
  await foldersStore.clear();

  for (const folder of data.folders) {
    await foldersStore.add(folder);
  }

  for (const prompt of data.prompts) {
    await promptsStore.add(prompt);
  }

  return tx.done;
};

export const clearAllFoldersDB = async (): Promise<void> => {
  const db = await getDB();
  await db.clear(FOLDERS_STORE_NAME);
};
