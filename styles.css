:root {
  /* Light theme colors - EVERYTHING light */
  --primary: #4338CA;
  --primary-light: #4F46E5;
  --primary-dark: #3730A3;
  --secondary: #059669;
  --text-primary: #111827;
  --text-secondary: #4B5563;
  --bg-primary: #FFFFFF;
  --bg-secondary: #F3F4F6;
  --border-color: #E5E7EB;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --shadow-color-hover: rgba(0, 0, 0, 0.15);
}

/* Dark theme - EVERYTHING dark - Support both data-theme and class selectors */
[data-theme='dark'],
.dark {
  --primary: #6366F1;
  --primary-light: #8B5CF6;
  --primary-dark: #4F46E5;
  --secondary: #10B981;
  --text-primary: #F9FAFB;
  --text-secondary: #D1D5DB;
  --bg-primary: #1F2937;
  --bg-secondary: #111827;
  --border-color: #374151;
  --shadow-color: rgba(0, 0, 0, 0.3);
  --shadow-color-hover: rgba(0, 0, 0, 0.4);
}

body {
  margin: 0;
  font-family: system-ui, -apple-system, sans-serif;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

/* Header styles */
.header {
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: 1rem;
  position: sticky;
  top: 0;
  z-index: 40;
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
}

.header-content {
  max-width: 1920px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Button styles */
.button {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s;
  cursor: pointer;
  border: none;
}

.button-primary {
  background-color: var(--primary);
  color: white;
}

.button-primary:hover {
  background-color: var(--primary-dark);
}

.button-secondary {
  background-color: var(--secondary);
  color: white;
}

.button-outline {
  border: 2px solid var(--primary);
  color: var(--primary);
  background: transparent;
}

.button-outline:hover {
  background-color: var(--primary);
  color: white;
}

/* Card styles */
.card {
  background-color: var(--bg-primary);
  border-radius: 1rem;
  border: 1px solid var(--border-color);
  padding: 1.5rem;
  transition: all 0.3s;
  box-shadow: 0 4px 6px -1px var(--shadow-color);
}

.card:hover {
  box-shadow: 0 10px 15px -3px var(--shadow-color-hover);
}

/* Input styles */
.input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid var(--border-color);
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px var(--primary-light);
}

.input::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

/* Search and form controls */
.search-input {
  width: 100%;
  padding-left: 2.5rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px 0 var(--shadow-color);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px var(--primary-light);
}

.search-input::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

.select-input {
  width: 100%;
  appearance: none;
  padding-left: 0.75rem;
  padding-right: 2.5rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px 0 var(--shadow-color);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.2s;
}

.select-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px var(--primary-light);
}

.search-icon,
.select-icon {
  color: var(--text-secondary);
  opacity: 0.7;
}

/* Tag styles */
.tag {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  background-color: var(--secondary);
  color: white;
  display: inline-flex;
  align-items: center;
}

/* Sidebar styles */
.sidebar {
  width: 18rem;
  background-color: var(--bg-primary);
  border-right: 1px solid var(--border-color);
  padding: 1rem;
  height: 100vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.sidebar-content {
  flex: 1;
}

.sidebar-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0 0.75rem;
}

.sidebar-title {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--text-secondary);
  letter-spacing: 0.05em;
}

.sidebar-divider {
  flex: 1;
  height: 1px;
  background-color: var(--border-color);
  margin-left: 0.75rem;
}

.folder-list {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.folder-container {
  margin-bottom: 0.125rem;
}

.folder-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
}

.folder-item:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.folder-item.active {
  background-color: var(--primary-light);
  color: white;
}

.folder-item .icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.5rem;
  color: inherit;
}

/* Layout */
.layout {
  display: flex;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

/* Header specific styles */
.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.icon-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem;
  background-color: var(--bg-secondary);
  border-radius: 0.5rem;
}

.icon-button {
  padding: 0.5rem;
  border-radius: 0.375rem;
  color: var(--text-secondary);
  transition: all 0.2s;
}

.icon-button:hover {
  color: var(--primary);
  background-color: var(--bg-primary);
}

.icon {
  width: 1.25rem;
  height: 1.25rem;
}

/* Responsive utilities */
.hide-mobile {
  display: none;
}

@media (min-width: 640px) {
  .hide-mobile {
    display: block;
  }

  .show-mobile {
    display: none;
  }
}

.hide-tablet {
  display: none;
}

@media (min-width: 768px) {
  .hide-tablet {
    display: block;
  }

  .show-tablet {
    display: none;
  }
}

/* Prompt Tile styles */
.prompt-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  padding: 1.5rem;
}

.prompt-content {
  flex: 1;
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.prompt-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary);
  margin-right: 0.5rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.prompt-actions {
  display: flex;
  gap: 0.25rem;
}

.prompt-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.prompt-version {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.prompt-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.prompt-tags {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.5rem;
}

.prompt-tags-more {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.prompt-footer {
  margin-top: 1.5rem;
}

.prompt-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.stat {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.stat-value {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-primary);
}

.prompt-actions-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.prompt-edit-actions {
  display: flex;
  gap: 0.25rem;
}

.button-success {
  background-color: #10B981;
  color: white;
}

.button-success:hover {
  background-color: #059669;
}

.icon-button-danger:hover {
  color: #EF4444;
  background-color: #FEE2E2;
}

/* Dark theme danger button */
[data-theme='dark'] .icon-button-danger:hover,
.dark .icon-button-danger:hover {
  color: #F87171;
  background-color: #7F1D1D;
}

/* App layout styles */
.app-container {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.loading-container {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

/* Empty state styles */
.empty-state {
  text-align: center;
  padding: 3rem 0;
}

.empty-state-icon {
  margin: 0 auto;
  height: 3rem;
  width: 3rem;
  color: var(--text-secondary);
  opacity: 0.7;
}

.empty-state-title {
  margin-top: 0.5rem;
  font-size: 1.125rem;
  font-weight: 500;
  color: var(--text-primary);
}

.empty-state-description {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.empty-state-link {
  font-weight: 500;
  color: var(--primary);
  transition: color 0.2s;
}

.empty-state-link:hover {
  color: var(--primary-dark);
}

/* Form styles */
.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.form-label-secondary {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-left: 0.25rem;
}

.form-input,
.form-textarea,
.form-select {
  margin-top: 0.25rem;
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 var(--shadow-color);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.2s;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px var(--primary-light);
}

.form-input.error,
.form-textarea.error,
.form-select.error {
  border-color: #EF4444;
}

.form-error {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #EF4444;
}

.form-error-container {
  border-radius: 0.375rem;
  background-color: #FEF2F2;
  padding: 1rem;
  margin-top: 1rem;
}

[data-theme='dark'] .form-error-container,
.dark .form-error-container {
  background-color: rgba(127, 29, 29, 0.3);
}

.form-error-icon {
  height: 1.25rem;
  width: 1.25rem;
  color: #F87171;
  flex-shrink: 0;
}

.form-error-text {
  margin-left: 0.75rem;
  font-size: 0.875rem;
  color: #B91C1C;
}

[data-theme='dark'] .form-error-text,
.dark .form-error-text {
  color: #FCA5A5;
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding-top: 0.5rem;
}

.button-cancel {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 var(--shadow-color);
  transition: all 0.2s;
  cursor: pointer;
}

.button-cancel:hover {
  background-color: var(--border-color);
}

.button-cancel:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary-light);
}

.button-primary {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: white;
  background-color: var(--primary);
  border: 1px solid transparent;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 var(--shadow-color);
  transition: all 0.2s;
  cursor: pointer;
}

.button-primary:hover {
  background-color: var(--primary-dark);
}

.button-primary:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary-light);
}

/* Tag input styles */
.tag-input-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 var(--shadow-color);
  background-color: var(--bg-primary);
  transition: all 0.2s;
}

.tag-input-container:focus-within {
  border-color: var(--primary);
  box-shadow: 0 0 0 1px var(--primary);
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background-color: var(--primary-light);
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
}

.tag-remove-button {
  margin-left: 0.25rem;
  color: var(--primary-dark);
  transition: color 0.2s;
  cursor: pointer;
}

.tag-remove-button:hover {
  color: white;
}

.tag-input-field {
  flex-grow: 1;
  padding: 0.25rem;
  border: none;
  outline: none;
  font-size: 0.875rem;
  background-color: transparent;
  color: var(--text-primary);
}

.tag-input-field::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}