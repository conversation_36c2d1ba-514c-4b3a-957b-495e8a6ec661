:root {
  /* Light theme colors - EVERYTHING light */
  --primary: #4338CA;
  --primary-light: #4F46E5;
  --primary-dark: #3730A3;
  --secondary: #059669;
  --text-primary: #111827;
  --text-secondary: #4B5563;
  --bg-primary: #FFFFFF;
  --bg-secondary: #F3F4F6;
  --border-color: #E5E7EB;
}

/* Dark theme - EVERYTHING dark */
[data-theme='dark'] {
  --primary: #4F46E5;
  --primary-light: #6366F1;
  --primary-dark: #4338CA;
  --secondary: #10B981;
  --text-primary: #F9FAFB;
  --text-secondary: #D1D5DB;
  --bg-primary: #1F2937;
  --bg-secondary: #111827;
  --border-color: #374151;
}

body {
  margin: 0;
  font-family: system-ui, -apple-system, sans-serif;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

/* Header styles */
.header {
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: 1rem;
  position: sticky;
  top: 0;
  z-index: 40;
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
}

.header-content {
  max-width: 1920px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Button styles */
.button {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s;
  cursor: pointer;
  border: none;
}

.button-primary {
  background-color: var(--primary);
  color: white;
}

.button-primary:hover {
  background-color: var(--primary-dark);
}

.button-secondary {
  background-color: var(--secondary);
  color: white;
}

.button-outline {
  border: 2px solid var(--primary);
  color: var(--primary);
  background: transparent;
}

.button-outline:hover {
  background-color: var(--primary);
  color: white;
}

/* Card styles */
.card {
  background-color: var(--bg-primary);
  border-radius: 1rem;
  border: 1px solid var(--border-color);
  padding: 1.5rem;
  transition: all 0.3s;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* Input styles */
.input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid var(--border-color);
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px var(--primary-light);
}

/* Tag styles */
.tag {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  background-color: var(--secondary);
  color: white;
  display: inline-flex;
  align-items: center;
}

/* Sidebar styles */
.sidebar {
  width: 18rem;
  background-color: var(--bg-primary);
  border-right: 1px solid var(--border-color);
  padding: 1rem;
  height: 100vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.sidebar-content {
  flex: 1;
}

.sidebar-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0 0.75rem;
}

.sidebar-title {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--text-secondary);
  letter-spacing: 0.05em;
}

.sidebar-divider {
  flex: 1;
  height: 1px;
  background-color: var(--border-color);
  margin-left: 0.75rem;
}

.folder-list {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.folder-container {
  margin-bottom: 0.125rem;
}

.folder-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
}

.folder-item:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.folder-item.active {
  background-color: var(--primary-light);
  color: white;
}

.folder-item .icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.5rem;
  color: inherit;
}

/* Layout */
.layout {
  display: flex;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

/* Header specific styles */
.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.icon-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem;
  background-color: var(--bg-secondary);
  border-radius: 0.5rem;
}

.icon-button {
  padding: 0.5rem;
  border-radius: 0.375rem;
  color: var(--text-secondary);
  transition: all 0.2s;
}

.icon-button:hover {
  color: var(--primary);
  background-color: var(--bg-primary);
}

.icon {
  width: 1.25rem;
  height: 1.25rem;
}

/* Responsive utilities */
.hide-mobile {
  display: none;
}

@media (min-width: 640px) {
  .hide-mobile {
    display: block;
  }

  .show-mobile {
    display: none;
  }
}

.hide-tablet {
  display: none;
}

@media (min-width: 768px) {
  .hide-tablet {
    display: block;
  }

  .show-tablet {
    display: none;
  }
}

/* Prompt Tile styles */
.prompt-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  padding: 1.5rem;
}

.prompt-content {
  flex: 1;
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.prompt-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary);
  margin-right: 0.5rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.prompt-actions {
  display: flex;
  gap: 0.25rem;
}

.prompt-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.prompt-version {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.prompt-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.prompt-tags {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.5rem;
}

.prompt-tags-more {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.prompt-footer {
  margin-top: 1.5rem;
}

.prompt-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.stat {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.stat-value {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-primary);
}

.prompt-actions-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.prompt-edit-actions {
  display: flex;
  gap: 0.25rem;
}

.button-success {
  background-color: #10B981;
  color: white;
}

.button-success:hover {
  background-color: #059669;
}

.icon-button-danger:hover {
  color: #EF4444;
  background-color: #FEE2E2;
}