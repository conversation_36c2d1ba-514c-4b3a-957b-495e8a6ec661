{"prompts": [{"id": "d1a7a8f7-c2d0-4f7a-8a4e-1b2a3c4d5e01", "originalPromptId": "opid-001", "version": 1, "title": "Blog Post Idea Generator", "description": "Generates 5 blog post ideas for a given keyword.", "promptText": "Generate 5 compelling blog post titles and brief outlines about {{topic}}. The tone should be {{tone_style}}.", "tags": ["blogging", "content creation", "marketing"], "folderId": "folder-mktg-01", "createdAt": "2023-10-01T10:00:00.000Z", "lastUsedAt": "2023-10-26T14:30:00.000Z", "timesUsed": 15}, {"id": "e2b8b9f8-d3e1-4g8b-9b5f-2c3b4d5e6f02", "originalPromptId": "opid-001", "version": 2, "title": "Advanced Blog Post Idea Generator", "description": "Generates 5 detailed blog post ideas with target audience for a given keyword.", "promptText": "For the keyword '{{topic}}', generate 5 compelling blog post titles. For each title, provide a brief 2-sentence outline and suggest a primary target audience. The tone should be {{tone_style}} and aimed at {{audience_level}}.", "tags": ["blogging", "seo", "marketing", "content strategy"], "folderId": "folder-mktg-01", "createdAt": "2023-10-15T11:00:00.000Z", "lastUsedAt": "2023-11-05T10:00:00.000Z", "timesUsed": 25}, {"id": "f3c9ca09-e4f2-4h9c-ac60-3d4c5e6f7003", "originalPromptId": "opid-002", "version": 1, "title": "Python Function Explainer", "description": "Explains a Python function in simple terms.", "promptText": "Explain the following Python code snippet like I'm a beginner: \n```python\n{{python_code}}\n```\nFocus on what it does and why it's useful.", "tags": ["python", "coding", "explanation"], "folderId": "folder-code-02", "createdAt": "2023-10-02T12:00:00.000Z", "lastUsedAt": "2023-11-01T16:00:00.000Z", "timesUsed": 8}, {"id": "g4d0db10-f503-4i0d-bd71-4e5d6f708114", "originalPromptId": "opid-003", "version": 1, "title": "Short Story Starter", "description": "Provides a starting paragraph for a short story based on a genre.", "promptText": "Write an intriguing opening paragraph (around 100 words) for a {{genre}} short story. The story should hint at a central mystery.", "tags": ["creative writing", "fiction", "story idea"], "folderId": "folder-creative-03", "createdAt": "2023-09-05T14:00:00.000Z", "lastUsedAt": "2023-10-20T11:00:00.000Z", "timesUsed": 12}, {"id": "h5e1ec21-0614-4j1e-ce82-5f6e70819225", "originalPromptId": "opid-003", "version": 2, "title": "Advanced Short Story Starter", "description": "Provides a starting paragraph and a character name for a short story.", "promptText": "Write an intriguing opening paragraph (around 100-150 words) for a {{genre}} short story featuring a character named {{character_name}}. The story should hint at a central mystery and a unique setting.", "tags": ["creative writing", "fiction", "story idea", "character development"], "folderId": "folder-creative-03", "createdAt": "2023-10-25T14:00:00.000Z", "lastUsedAt": "2023-11-03T18:00:00.000Z", "timesUsed": 5}, {"id": "i6f2fd32-1725-4k2f-df93-607f81920336", "originalPromptId": "opid-004", "version": 1, "title": "Email Subject Line Creator", "description": "Creates 3 catchy email subject lines.", "promptText": "Generate 3 catchy and click-worthy email subject lines for an email about {{email_topic}}.", "tags": ["email marketing", "copywriting"], "folderId": "folder-mktg-01", "createdAt": "2023-11-01T09:00:00.000Z", "lastUsedAt": "2023-11-06T09:15:00.000Z", "timesUsed": 22}, {"id": "j7030e43-2836-4l30-e0a4-718092031447", "originalPromptId": "opid-005", "version": 1, "title": "JavaScript Array Method Finder", "description": "Suggests a JS array method for a specific task.", "promptText": "I want to {{array_task}} in JavaScript. What's the best modern array method to use and can you provide a simple example?", "tags": ["javascript", "coding", "arrays"], "folderId": "folder-code-02", "createdAt": "2023-10-28T17:00:00.000Z", "lastUsedAt": "2023-11-04T12:30:00.000Z", "timesUsed": 18}, {"id": "k8141f54-3947-4m41-f1b5-829103142558", "originalPromptId": "opid-006", "version": 1, "title": "Poem Theme Generator", "description": "Generates a theme for a short poem.", "promptText": "Suggest a unique and thought-provoking theme for a short poem (around 4-8 lines).", "tags": ["creative writing", "poetry"], "folderId": "folder-creative-03", "createdAt": "2023-08-15T10:00:00.000Z", "lastUsedAt": "2023-10-10T10:00:00.000Z", "timesUsed": 30}, {"id": "l9252065-4a58-4n52-02c6-930214253669", "originalPromptId": "opid-007", "version": 1, "title": "Product Description Enhancer", "description": "Rewrites a product description to be more persuasive.", "promptText": "Rewrite the following product description to be more persuasive and highlight its key benefits. Original description: {{product_description}}", "tags": ["copywriting", "ecommerce", "marketing"], "folderId": "folder-mktg-01", "createdAt": "2023-11-02T11:30:00.000Z", "lastUsedAt": "2023-11-06T11:35:00.000Z", "timesUsed": 11}, {"id": "m0363176-5b69-4o63-13d7-a41325364770", "originalPromptId": "opid-007", "version": 2, "title": "Benefit-Driven Product Description", "description": "Creates a benefit-driven product description from features.", "promptText": "Take these product features: {{product_features}}. Write a persuasive, benefit-driven product description (around 100 words) for {{target_customer}}.", "tags": ["copywriting", "ecommerce", "marketing", "persuasion"], "folderId": "folder-mktg-01", "createdAt": "2023-11-05T14:30:00.000Z", "lastUsedAt": "2023-11-07T10:00:00.000Z", "timesUsed": 5}, {"id": "n1474287-6c7a-4p74-24e8-b52436475881", "originalPromptId": "opid-008", "version": 1, "title": "SQL Query Optimizer", "description": "Suggests optimizations for a given SQL query.", "promptText": "How can I optimize the following SQL query for better performance?\n```sql\n{{sql_query}}\n```\nExplain the reasons for your suggestions.", "tags": ["sql", "database", "performance"], "folderId": "folder-code-02", "createdAt": "2023-10-12T13:15:00.000Z", "lastUsedAt": "2023-10-30T13:20:00.000Z", "timesUsed": 7}, {"id": "o2585398-7d8b-4q85-35f9-c63547586992", "originalPromptId": "opid-009", "version": 1, "title": "Worldbuilding Detail - Culture", "description": "Generates a unique cultural detail for a fantasy world.", "promptText": "Describe a unique cultural festival or tradition for a fantasy world where {{world_premise}}.", "tags": ["creative writing", "worldbuilding", "fantasy"], "folderId": "folder-creative-03", "createdAt": "2023-09-20T16:00:00.000Z", "lastUsedAt": "2023-10-18T16:05:00.000Z", "timesUsed": 14}, {"id": "p3696409-8e9c-4r96-460a-d74658697003", "originalPromptId": "opid-010", "version": 1, "title": "Social Media Post - Event", "description": "Drafts a short social media post for an upcoming event.", "promptText": "Draft a short, engaging social media post (for Twitter/X) announcing an upcoming event: {{event_name}} on {{event_date}}. Include a call to action: {{cta}}.", "tags": ["social media", "marketing", "event promotion"], "folderId": null, "createdAt": "2023-11-03T10:00:00.000Z", "lastUsedAt": "2023-11-07T11:00:00.000Z", "timesUsed": 19}, {"id": "q4707510-9f0d-4s07-571b-e85769708114", "originalPromptId": "opid-011", "version": 1, "title": "Code Comment Generator", "description": "Generates a clear comment for a block of code.", "promptText": "Write a concise and clear comment explaining what the following code block does:\n```{{language}}\n{{code_block}}\n```", "tags": ["coding", "documentation"], "folderId": "folder-code-02", "createdAt": "2023-10-05T15:45:00.000Z", "lastUsedAt": "2023-10-29T15:50:00.000Z", "timesUsed": 9}, {"id": "r5818621-a01e-4t18-682c-f96870819225", "originalPromptId": "opid-011", "version": 2, "title": "Function Docstring Generator (Python)", "description": "Generates a Python docstring for a function.", "promptText": "Write a PEP 257 compliant docstring for the following Python function:\n```python\n{{python_function_code}}\n```", "tags": ["python", "coding", "documentation", "docstring"], "folderId": "folder-code-02", "createdAt": "2023-10-20T10:00:00.000Z", "lastUsedAt": "2023-11-02T10:05:00.000Z", "timesUsed": 13}, {"id": "s6929732-b12f-4u29-793d-0a7981920336", "originalPromptId": "opid-012", "version": 1, "title": "Character Dialogue Writer", "description": "Writes a short dialogue between two characters.", "promptText": "Write a short piece of dialogue (4-6 lines) between two characters, {{char1_name}} (who is {{char1_trait}}) and {{char2_name}} (who is {{char2_trait}}), about {{dialogue_topic}}.", "tags": ["creative writing", "dialogue", "fiction"], "folderId": "folder-creative-03", "createdAt": "2023-08-22T11:00:00.000Z", "lastUsedAt": "2023-10-22T11:05:00.000Z", "timesUsed": 20}, {"id": "t7030843-c230-4v30-8a4e-1b8092031447", "originalPromptId": "opid-013", "version": 1, "title": "Ad Copy Variation", "description": "Generates two variations of an ad copy.", "promptText": "Generate two different versions of ad copy (max 30 words each) for a product called {{product_name}} that helps with {{product_benefit}}.", "tags": ["marketing", "advertising", "copywriting"], "folderId": "folder-mktg-01", "createdAt": "2023-11-04T14:00:00.000Z", "lastUsedAt": "2023-11-06T14:05:00.000Z", "timesUsed": 6}, {"id": "u8141954-d341-4w41-9b5f-2c9103142558", "originalPromptId": "opid-014", "version": 1, "title": "API Endpoint Naming", "description": "Suggests RESTful API endpoint names for a resource.", "promptText": "Suggest RESTful API endpoint names (including HTTP methods) for managing a '{{resource_name}}' resource.", "tags": ["api", "coding", "rest"], "folderId": null, "createdAt": "2023-10-18T09:30:00.000Z", "lastUsedAt": "2023-11-01T09:35:00.000Z", "timesUsed": 10}, {"id": "v9252065-e452-4x52-ac60-3d0214253669", "originalPromptId": "opid-015", "version": 1, "title": "Flash Fiction Prompt", "description": "A prompt for a very short story (flash fiction).", "promptText": "Write a complete flash fiction story (under 300 words) based on the following three words: {{word1}}, {{word2}}, {{word3}}.", "tags": ["creative writing", "flash fiction", "story prompt"], "folderId": "folder-creative-03", "createdAt": "2023-07-30T12:00:00.000Z", "lastUsedAt": "2023-10-28T12:05:00.000Z", "timesUsed": 28}, {"id": "w0363176-f563-4y63-bd71-4e1325364770", "originalPromptId": "opid-015", "version": 2, "title": "Image-Inspired Flash Fiction", "description": "A flash fiction prompt inspired by an image description.", "promptText": "Write a complete flash fiction story (under 300 words) inspired by this image description: A {{image_description}}.", "tags": ["creative writing", "flash fiction", "visual prompt"], "folderId": "folder-creative-03", "createdAt": "2023-09-15T14:20:00.000Z", "lastUsedAt": "2023-11-05T14:25:00.000Z", "timesUsed": 3}, {"id": "x1474287-0674-4z74-ce82-5f2436475881", "originalPromptId": "opid-016", "version": 1, "title": "Personalized Thank You Note", "description": "Drafts a personalized thank you note.", "promptText": "Draft a short, sincere thank you note to {{recipient_name}} for {{reason_for_thanks}}. Mention specifically {{specific_detail}}.", "tags": ["communication", "etiquette"], "folderId": null, "createdAt": "2023-11-05T10:10:00.000Z", "lastUsedAt": "2023-11-07T10:15:00.000Z", "timesUsed": 4}, {"id": "y2585398-1785-4a85-df93-603547586992", "originalPromptId": "opid-017", "version": 1, "title": "Meeting Agenda Creator", "description": "Creates a basic meeting agenda.", "promptText": "Create a basic agenda for a {{meeting_duration_hours}} hour meeting about {{meeting_topic}}. Include time slots for introduction, {{discussion_point_1}}, {{discussion_point_2}}, and Q&A.", "tags": ["productivity", "meetings"], "folderId": null, "createdAt": "2023-10-26T17:00:00.000Z", "lastUsedAt": "2023-10-26T17:05:00.000Z", "timesUsed": 2}, {"id": "z3696409-2896-4b96-e0a4-714658697003", "originalPromptId": "opid-018", "version": 1, "title": "Code Refactoring Ideas", "description": "Suggests ideas for refactoring a piece of code.", "promptText": "I have a piece of code that {{code_description}}. What are some general principles or specific ideas I could apply to refactor it for better {{refactor_goal_1}} and {{refactor_goal_2}}?", "tags": ["coding", "refactoring", "software design"], "folderId": "folder-code-02", "createdAt": "2023-11-06T15:00:00.000Z", "lastUsedAt": null, "timesUsed": 0}, {"id": "a4707510-3907-4c07-f1b5-825769708114", "originalPromptId": "opid-018", "version": 2, "title": "Specific Code Refactoring", "description": "Suggests refactoring for a specific code snippet.", "promptText": "Given the following {{language}} code:\n```{{language}}\n{{code_snippet}}\n```\nSuggest specific ways to refactor it to improve {{refactor_goal}}. Explain your reasoning.", "tags": ["coding", "refactoring", "software design", "code review"], "folderId": "folder-code-02", "createdAt": "2023-11-07T09:00:00.000Z", "lastUsedAt": "2023-11-07T09:05:00.000Z", "timesUsed": 1}, {"id": "b5818621-4a18-4d18-02c6-936870819225", "originalPromptId": "opid-019", "version": 1, "title": "Debate Point Summarizer", "description": "Summarizes key points for one side of a debate.", "promptText": "Summarize 3 key arguments for the '{{debate_side}}' position on the topic of '{{debate_topic}}'. Keep each argument concise (1-2 sentences).", "tags": ["debate", "argumentation", "summary"], "folderId": null, "createdAt": "2023-10-03T16:30:00.000Z", "lastUsedAt": "2023-10-17T16:35:00.000Z", "timesUsed": 16}, {"id": "c6929732-5b29-4e29-13d7-a47981920336", "originalPromptId": "opid-020", "version": 1, "title": "Explain Like I'm Five (ELI5)", "description": "Explains a complex topic in very simple terms.", "promptText": "Explain '{{complex_topic}}' to me like I'm five years old.", "tags": ["explanation", "eli5", "simplification"], "folderId": null, "createdAt": "2023-09-01T11:00:00.000Z", "lastUsedAt": "2023-11-04T11:05:00.000Z", "timesUsed": 35}], "folders": [{"id": "folder-mktg-01", "name": "Marketing Content", "parentId": null, "isDeletable": true, "isRenamable": true}, {"id": "folder-code-02", "name": "Coding Assistants", "parentId": null, "isDeletable": true, "isRenamable": true}, {"id": "folder-creative-03", "name": "Creative Writing", "parentId": null, "isDeletable": true, "isRenamable": true}, {"id": "folder-social-sub", "name": "Social Media", "parentId": "folder-mktg-01", "isDeletable": true, "isRenamable": true}]}